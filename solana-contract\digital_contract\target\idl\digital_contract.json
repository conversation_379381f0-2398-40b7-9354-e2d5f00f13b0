{"version": "0.1.0", "name": "digital_contract", "instructions": [{"name": "createContract", "accounts": [{"name": "contract", "isMut": true, "isSigner": false}, {"name": "creator", "isMut": true, "isSigner": true}, {"name": "platformFeeRecipient", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "documentHash", "type": "string"}, {"name": "title", "type": "string"}, {"name": "parties", "type": {"vec": "public<PERSON>ey"}}, {"name": "mediator", "type": {"option": "public<PERSON>ey"}}, {"name": "expiryTimestamp", "type": {"option": "i64"}}]}, {"name": "signContract", "accounts": [{"name": "contract", "isMut": true, "isSigner": false}, {"name": "signer", "isMut": false, "isSigner": true}], "args": []}], "accounts": [{"name": "Contract", "type": {"kind": "struct", "fields": [{"name": "creator", "type": "public<PERSON>ey"}, {"name": "documentHash", "type": "string"}, {"name": "title", "type": "string"}, {"name": "parties", "type": {"vec": "public<PERSON>ey"}}, {"name": "mediator", "type": {"option": "public<PERSON>ey"}}, {"name": "signatures", "type": {"vec": "bool"}}, {"name": "status", "type": {"defined": "ContractStatus"}}, {"name": "createdAt", "type": "i64"}, {"name": "expiryTimestamp", "type": {"option": "i64"}}, {"name": "isFullySigned", "type": "bool"}]}}], "types": [{"name": "ContractStatus", "type": {"kind": "enum", "variants": [{"name": "Active"}, {"name": "Completed"}, {"name": "Cancelled"}]}}], "errors": [{"code": 6000, "name": "DocumentHashTooLong", "msg": "Document hash is too long"}, {"code": 6001, "name": "TitleTooLong", "msg": "Title is too long"}, {"code": 6002, "name": "InvalidPartyCount", "msg": "Invalid number of parties"}, {"code": 6003, "name": "ContractExpired", "msg": "Contract has expired"}, {"code": 6004, "name": "AlreadySigned", "msg": "Party has already signed"}, {"code": 6005, "name": "NotAuthorized", "msg": "Not authorized to sign this contract"}, {"code": 6006, "name": "ContractNotActive", "msg": "Contract is not active"}]}